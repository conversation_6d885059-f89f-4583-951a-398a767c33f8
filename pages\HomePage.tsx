
import React from 'react';
import HeroSection from '../components/home/<USER>';
import AboutSection from '../components/home/<USER>';
import CtaSection from '../components/home/<USER>';
import CryptoTicker from '../components/home/<USER>';
import FeaturedNfts from '../components/home/<USER>';
import Web3DataPanel from '../components/web3/Web3DataPanel';
import DeFiSimulator from '../components/web3/DeFiSimulator';

const HomePage: React.FC = () => {
  return (
    <div className="space-y-16 md:space-y-24 pb-16">
      <HeroSection />
      <CryptoTicker />

      {/* Web3 Data Panel Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8">
        <Web3DataPanel />
      </section>

      <AboutSection />
      <FeaturedNfts />

      {/* DeFi Simulator Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8">
        <DeFiSimulator />
      </section>

      <CtaSection />
    </div>
  );
};

export default HomePage;
    