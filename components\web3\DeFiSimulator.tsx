import React, { useState, useEffect } from 'react';
import HolographicCard from '../ui/HolographicCard';
import NeonButton from '../ui/NeonButton';
import { CurrencyDollarIcon, ChartBarIcon, BoltIcon, SparklesIcon } from '../ui/Icons';

interface Pool {
  id: string;
  name: string;
  token1: string;
  token2: string;
  apy: number;
  tvl: string;
  risk: 'Low' | 'Medium' | 'High';
  rewards: string[];
}

const DeFiSimulator: React.FC = () => {
  const [selectedPool, setSelectedPool] = useState<Pool | null>(null);
  const [stakeAmount, setStakeAmount] = useState<string>('');
  const [isStaking, setIsStaking] = useState(false);
  const [earnings, setEarnings] = useState(0);
  const [stakingTime, setStakingTime] = useState(0);

  const pools: Pool[] = [
    {
      id: '1',
      name: 'ETH-USDC',
      token1: 'ETH',
      token2: 'USDC',
      apy: 12.5,
      tvl: '$2.4B',
      risk: 'Low',
      rewards: ['COMP', 'LDO'],
    },
    {
      id: '2',
      name: 'BILLIE-ETH',
      token1: 'BILLIE',
      token2: 'ETH',
      apy: 45.8,
      tvl: '$125M',
      risk: 'High',
      rewards: ['BILLIE', 'HONEY'],
    },
    {
      id: '3',
      name: 'BTC-WETH',
      token1: 'BTC',
      token2: 'WETH',
      apy: 8.2,
      tvl: '$1.8B',
      risk: 'Low',
      rewards: ['CRV'],
    },
    {
      id: '4',
      name: 'MATIC-USDT',
      token1: 'MATIC',
      token2: 'USDT',
      apy: 28.7,
      tvl: '$450M',
      risk: 'Medium',
      rewards: ['MATIC', 'QI'],
    },
  ];

  useEffect(() => {
    if (isStaking && selectedPool && stakeAmount) {
      const interval = setInterval(() => {
        setStakingTime(prev => prev + 1);
        const amount = parseFloat(stakeAmount) || 0;
        const yearlyEarnings = amount * (selectedPool.apy / 100);
        const secondlyEarnings = yearlyEarnings / (365 * 24 * 60 * 60);
        setEarnings(prev => prev + secondlyEarnings);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isStaking, selectedPool, stakeAmount]);

  const handleStake = () => {
    if (selectedPool && stakeAmount) {
      setIsStaking(true);
      setEarnings(0);
      setStakingTime(0);
    }
  };

  const handleUnstake = () => {
    setIsStaking(false);
    setEarnings(0);
    setStakingTime(0);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'green';
      case 'Medium': return 'gold';
      case 'High': return 'red';
      default: return 'purple';
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-display font-bold text-billieGold animate-glow mb-2">
          DeFi Yield Farming Simulator
        </h2>
        <p className="text-neutral-400 font-cyber">
          Experience the thrill of yield farming without the risk
        </p>
      </div>

      {/* Pool Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {pools.map((pool) => (
          <HolographicCard
            key={pool.id}
            glowColor={selectedPool?.id === pool.id ? 'purple' : getRiskColor(pool.risk) as any}
            intensity={selectedPool?.id === pool.id ? 'high' : 'medium'}
            className={`p-4 cursor-pointer transition-all duration-300 ${
              selectedPool?.id === pool.id ? 'ring-2 ring-neonPurple' : ''
            }`}
            onClick={() => setSelectedPool(pool)}
          >
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-display font-bold text-billieHeading">{pool.name}</h3>
                <span className={`px-2 py-1 rounded text-xs font-cyber ${
                  pool.risk === 'Low' ? 'bg-neonGreen/20 text-neonGreen' :
                  pool.risk === 'Medium' ? 'bg-neonGold/20 text-neonGold' :
                  'bg-neonRed/20 text-neonRed'
                }`}>
                  {pool.risk}
                </span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-neutral-400">APY</span>
                  <span className="text-neonGreen font-bold">{pool.apy}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-neutral-400">TVL</span>
                  <span className="text-billieGold">{pool.tvl}</span>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-1">
                {pool.rewards.map((reward, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-neonCyan/20 text-neonCyan text-xs rounded font-cyber"
                  >
                    {reward}
                  </span>
                ))}
              </div>
            </div>
          </HolographicCard>
        ))}
      </div>

      {/* Staking Interface */}
      {selectedPool && (
        <HolographicCard glowColor="purple" intensity="high" className="p-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-xl font-display font-bold text-billieGold animate-glow">
                Stake in {selectedPool.name} Pool
              </h3>
              
              <div className="space-y-2">
                <label className="block text-sm font-cyber text-neutral-400 uppercase tracking-wider">
                  Amount to Stake
                </label>
                <input
                  type="number"
                  value={stakeAmount}
                  onChange={(e) => setStakeAmount(e.target.value)}
                  placeholder="0.00"
                  className="w-full px-4 py-3 bg-neutral-800/50 border border-neonPurple/30 rounded-lg text-billieHeading focus:border-neonPurple focus:ring-2 focus:ring-neonPurple/20 outline-none transition-all duration-300"
                  disabled={isStaking}
                />
              </div>

              <div className="flex gap-3">
                {!isStaking ? (
                  <NeonButton
                    variant="purple"
                    size="md"
                    onClick={handleStake}
                    disabled={!stakeAmount || parseFloat(stakeAmount) <= 0}
                    leftIcon={<SparklesIcon className="w-4 h-4" />}
                  >
                    Start Farming
                  </NeonButton>
                ) : (
                  <NeonButton
                    variant="red"
                    size="md"
                    onClick={handleUnstake}
                    leftIcon={<BoltIcon className="w-4 h-4" />}
                  >
                    Unstake
                  </NeonButton>
                )}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-display font-bold text-neonCyan animate-glow">
                Farming Stats
              </h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-neutral-800/30 p-3 rounded-lg border border-neonGreen/30">
                  <p className="text-xs text-neutral-400 font-cyber uppercase">Staked</p>
                  <p className="text-lg font-bold text-neonGreen">
                    {isStaking ? stakeAmount : '0.00'} {selectedPool.token1}
                  </p>
                </div>
                
                <div className="bg-neutral-800/30 p-3 rounded-lg border border-neonGold/30">
                  <p className="text-xs text-neutral-400 font-cyber uppercase">Earned</p>
                  <p className="text-lg font-bold text-neonGold">
                    {earnings.toFixed(6)} {selectedPool.token1}
                  </p>
                </div>
                
                <div className="bg-neutral-800/30 p-3 rounded-lg border border-neonCyan/30">
                  <p className="text-xs text-neutral-400 font-cyber uppercase">Time</p>
                  <p className="text-lg font-bold text-neonCyan font-cyber">
                    {formatTime(stakingTime)}
                  </p>
                </div>
                
                <div className="bg-neutral-800/30 p-3 rounded-lg border border-neonPurple/30">
                  <p className="text-xs text-neutral-400 font-cyber uppercase">APY</p>
                  <p className="text-lg font-bold text-neonPurple">
                    {selectedPool.apy}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        </HolographicCard>
      )}
    </div>
  );
};

export default DeFiSimulator;
