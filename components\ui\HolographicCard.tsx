import React, { useRef, useState } from 'react';

interface HolographicCardProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'low' | 'medium' | 'high';
  glowColor?: 'purple' | 'gold' | 'red' | 'cyan' | 'green';
  onClick?: () => void;
}

const HolographicCard: React.FC<HolographicCardProps> = ({
  children,
  className = '',
  intensity = 'medium',
  glowColor = 'purple',
  onClick,
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    setMousePosition({ x, y });
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  const intensityClasses = {
    low: 'animate-hologram',
    medium: 'animate-hologram animate-neon-pulse',
    high: 'animate-hologram animate-neon-pulse animate-glow',
  };

  const glowColorClasses = {
    purple: 'shadow-glow-purple border-neonPurple',
    gold: 'shadow-glow-gold border-neonGold',
    red: 'shadow-glow-red border-neonRed',
    cyan: 'shadow-neon border-neonCyan',
    green: 'shadow-neon border-neonGreen',
  };

  const gradientStyle = isHovered
    ? {
        background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(139, 92, 246, 0.1) 0%, transparent 50%)`,
      }
    : {};

  return (
    <div
      ref={cardRef}
      className={`
        relative overflow-hidden rounded-xl backdrop-blur-md bg-neutral-900/50 border-2 transition-all duration-300 ease-in-out transform hover:scale-[1.02] cursor-pointer group
        ${intensityClasses[intensity]}
        ${glowColorClasses[glowColor]}
        ${isHovered ? 'shadow-neon-lg' : 'shadow-cyber'}
        ${className}
      `}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      {/* Animated background gradient */}
      <div
        className="absolute inset-0 opacity-30 transition-opacity duration-300"
        style={gradientStyle}
      />

      {/* Scanning lines effect */}
      <div className="absolute inset-0 opacity-20">
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-full h-px bg-gradient-to-r from-transparent via-current to-transparent animate-data-stream"
            style={{
              top: `${20 + i * 20}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: '3s',
            }}
          />
        ))}
      </div>

      {/* Corner brackets */}
      <div className="absolute top-2 left-2 w-4 h-4 border-t-2 border-l-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />
      <div className="absolute top-2 right-2 w-4 h-4 border-t-2 border-r-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />
      <div className="absolute bottom-2 left-2 w-4 h-4 border-b-2 border-l-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />
      <div className="absolute bottom-2 right-2 w-4 h-4 border-b-2 border-r-2 border-current opacity-60 group-hover:opacity-100 transition-opacity" />

      {/* Grid overlay */}
      <div 
        className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity"
        style={{
          backgroundImage: 'linear-gradient(rgba(139, 92, 246, 0.5) 1px, transparent 1px), linear-gradient(90deg, rgba(139, 92, 246, 0.5) 1px, transparent 1px)',
          backgroundSize: '20px 20px',
        }}
      />

      {/* Content */}
      <div className="relative z-10 p-6">
        {children}
      </div>

      {/* Holographic shimmer effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-5 transition-opacity duration-500 transform -translate-x-full group-hover:translate-x-full group-hover:transition-transform group-hover:duration-1000" />
    </div>
  );
};

export default HolographicCard;
