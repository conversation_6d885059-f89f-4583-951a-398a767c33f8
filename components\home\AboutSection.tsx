
import React, { Suspense } from 'react';
import { <PERSON> } from 'react-router-dom';
import Button from '../ui/Button';
import NeonButton from '../ui/NeonButton';
import HolographicCard from '../ui/HolographicCard';
import BillieSitting3D from '../billie/BillieSitting3D';
import { BILLIE_QUOTE } from '../../constants';
import { ChevronRightIcon, SparklesIcon, CpuChipIcon, BoltIcon } from '../ui/Icons';

const AboutSection: React.FC = () => {
  const web3Features = [
    {
      icon: <CpuChipIcon className="w-8 h-8" />,
      title: "DeFi Native",
      description: "Born in the world of decentralized finance",
      color: "purple"
    },
    {
      icon: <SparklesIcon className="w-8 h-8" />,
      title: "NFT Pioneer",
      description: "Leading the digital collectibles revolution",
      color: "gold"
    },
    {
      icon: <BoltIcon className="w-8 h-8" />,
      title: "Web3 Educator",
      description: "Teaching the next generation of crypto enthusiasts",
      color: "cyan"
    }
  ];

  return (
    <section className="py-16 bg-neutral-900/50 backdrop-blur-sm relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-neonPurple rounded-full animate-particle-float opacity-40"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${i * 2}s`,
              animationDuration: `${10 + i}s`,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-display text-3xl sm:text-4xl font-bold text-billieGold mb-6 animate-glow">
              Who is <span className="text-neonPurple animate-neon-pulse">Billie</span>?
            </h2>

            {/* Enhanced 3D Billie model */}
            <div className="relative inline-block mb-8">
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-neonPurple via-neonGold to-neonCyan animate-spin-slow opacity-30 blur-xl scale-110" />
              <Suspense fallback={
                <div className="w-40 h-40 md:w-48 md:h-48 mx-auto bg-gradient-to-br from-neonPurple/20 via-billieGold/20 to-neonCyan/20 rounded-full flex items-center justify-center animate-float border-2 border-neonPurple animate-neon-pulse shadow-glow-purple">
                  <div className="text-billieGold text-sm font-semibold animate-glow font-display">Loading Billie...</div>
                </div>
              }>
                <BillieSitting3D />
              </Suspense>
            </div>
          </div>

          <HolographicCard glowColor="purple" intensity="medium" className="p-8 mb-8">
            <p className="text-lg text-billieBodyText mb-6 text-center">
              Billie isn't your average forest dweller. Forged in the fires of volatile crypto markets and enlightened by the endless possibilities of blockchain, Billie is a Web3 evangelist with a taste for innovation (and FOMO tears).
            </p>

            <blockquote className="italic text-xl text-billieAccent border-l-4 border-neonPurple pl-6 py-4 my-8 max-w-2xl mx-auto bg-neutral-800/30 rounded-r-lg animate-neon-pulse">
              <span className="text-neonGold animate-glow">"{BILLIE_QUOTE}"</span>
            </blockquote>

            <p className="text-lg text-billieBodyText mb-8 text-center">
              From navigating the treacherous bear market to riding the exhilarating waves of the bull run, Billie has learned, adapted, and thrived. Now, he's here to share his journey and insights into the revolutionary world of Web3.
            </p>
          </HolographicCard>

          {/* Web3 Features Grid */}
          <div className="grid md:grid-cols-3 gap-6 mb-8">
            {web3Features.map((feature, index) => (
              <HolographicCard
                key={index}
                glowColor={feature.color as any}
                intensity="low"
                className="p-6 text-center"
              >
                <div className={`inline-flex p-3 rounded-full mb-4 ${
                  feature.color === 'purple' ? 'bg-neonPurple/20 text-neonPurple' :
                  feature.color === 'gold' ? 'bg-neonGold/20 text-neonGold' :
                  'bg-neonCyan/20 text-neonCyan'
                } animate-neon-pulse`}>
                  {feature.icon}
                </div>
                <h3 className="text-lg font-display font-bold text-billieHeading mb-2 animate-glow">
                  {feature.title}
                </h3>
                <p className="text-sm text-neutral-300">{feature.description}</p>
              </HolographicCard>
            ))}
          </div>

          <div className="text-center">
            <Link to="/story">
              <NeonButton
                variant="gold"
                size="lg"
                rightIcon={<ChevronRightIcon className="w-5 h-5" />}
                glowIntensity="high"
              >
                Discover Billie's Full Story
              </NeonButton>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
    