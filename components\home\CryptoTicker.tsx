
import React, { useState, useEffect } from 'react';
import { MOCK_CRYPTO_PRICES } from '../../constants';
import { CryptoPrice } from '../../types';

const CryptoTicker: React.FC = () => {
  const [prices, setPrices] = useState<CryptoPrice[]>(MOCK_CRYPTO_PRICES);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
useEffect(() => {
    // In a real app, you'd fetch this data from an API
    // and update it periodically. For this mock, we'll just
    // slightly vary the prices to show change.
    const interval = setInterval(() => {
      setPrices(prevPrices =>
        prevPrices.map(coin => {
          const priceNum = parseFloat(coin.price.replace('$', '').replace(',', ''));
          const change = (Math.random() - 0.5) * (priceNum * 0.01); // +/- 1% change
          const newPrice = priceNum + change;
          const change24hNum = parseFloat(coin.change24h.replace('%', ''));
          const newChange24h = change24hNum + (Math.random() - 0.5) * 0.1; // Fluctuate change %

          return {
            ...coin,
            price: `$${newPrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
            change24h: `${newChange24h >= 0 ? '+' : ''}${newChange24h.toFixed(1)}%`,
          };
        })
      );
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);


  return (
    <section className="bg-neutral-800/50 py-4 overflow-hidden border-y border-neonPurple/20 relative">
      {/* Animated background lines */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 3 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-full h-px bg-gradient-to-r from-transparent via-neonCyan to-transparent animate-data-stream opacity-30"
            style={{
              top: `${30 + i * 20}%`,
              animationDelay: `${i * 2}s`,
              animationDuration: '8s',
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="relative flex">
          <div className="animate-marquee flex space-x-8">
            {prices.concat(prices).map((coin, index) => ( // Duplicate for seamless loop
              <div key={`${coin.id}-${index}`} className="flex items-center space-x-3 min-w-max p-3 rounded-lg hover:bg-neutral-700/50 transition-all duration-300 hover:shadow-neon border border-transparent hover:border-neonCyan/30 group">
                {coin.iconUrl && <img src={coin.iconUrl} alt={coin.name} className="w-6 h-6 rounded-full group-hover:animate-neon-pulse" />}
                <div>
                  <span className="font-semibold text-sm text-billieHeading group-hover:text-neonCyan transition-colors">{coin.symbol}</span>
                  <span className="text-sm text-billieBodyText ml-2 font-cyber">{coin.price}</span>
                </div>
                <span className={`text-xs font-medium ml-1 font-cyber font-bold ${coin.change24h.startsWith('+') ? 'text-neonGreen animate-glow' : 'text-neonRed animate-neon-pulse'}`}>
                  {coin.change24h}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
      <style>{`
        @keyframes marquee {
          0% { transform: translateX(0%); }
          100% { transform: translateX(-50%); }
        }
        .animate-marquee {
          animation: marquee 40s linear infinite;
        }
      `}</style>
    </section>
  );
};

export default CryptoTicker;
