import React, { useState, useEffect } from 'react';
import HolographicCard from '../ui/HolographicCard';
import { CpuChipIcon, BoltIcon, CurrencyDollarIcon, ChartBarIcon } from '../ui/Icons';

interface Web3Data {
  gasPrice: number;
  blockNumber: number;
  ethPrice: number;
  totalValueLocked: string;
  activeUsers: number;
  networkHashRate: string;
}

const Web3DataPanel: React.FC = () => {
  const [web3Data, setWeb3Data] = useState<Web3Data>({
    gasPrice: 25,
    blockNumber: 18500000,
    ethPrice: 3005.12,
    totalValueLocked: '45.2B',
    activeUsers: 1250000,
    networkHashRate: '850 TH/s',
  });

  const [isLive, setIsLive] = useState(true);

  useEffect(() => {
    if (!isLive) return;

    const interval = setInterval(() => {
      setWeb3Data(prev => ({
        gasPrice: Math.max(15, prev.gasPrice + (Math.random() - 0.5) * 10),
        blockNumber: prev.blockNumber + Math.floor(Math.random() * 3),
        ethPrice: Math.max(2000, prev.ethPrice + (Math.random() - 0.5) * 50),
        totalValueLocked: `${(45.2 + (Math.random() - 0.5) * 2).toFixed(1)}B`,
        activeUsers: Math.max(1000000, prev.activeUsers + Math.floor((Math.random() - 0.5) * 10000)),
        networkHashRate: `${(850 + (Math.random() - 0.5) * 50).toFixed(0)} TH/s`,
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, [isLive]);

  const dataItems = [
    {
      label: 'Gas Price',
      value: `${web3Data.gasPrice.toFixed(0)} gwei`,
      icon: <BoltIcon className="w-5 h-5" />,
      color: web3Data.gasPrice > 30 ? 'red' : web3Data.gasPrice > 20 ? 'gold' : 'green',
      trend: web3Data.gasPrice > 25 ? '↑' : '↓',
    },
    {
      label: 'Block Height',
      value: web3Data.blockNumber.toLocaleString(),
      icon: <CpuChipIcon className="w-5 h-5" />,
      color: 'purple',
      trend: '↑',
    },
    {
      label: 'ETH Price',
      value: `$${web3Data.ethPrice.toFixed(2)}`,
      icon: <CurrencyDollarIcon className="w-5 h-5" />,
      color: 'gold',
      trend: web3Data.ethPrice > 3000 ? '↑' : '↓',
    },
    {
      label: 'TVL',
      value: web3Data.totalValueLocked,
      icon: <ChartBarIcon className="w-5 h-5" />,
      color: 'cyan',
      trend: '↑',
    },
  ];

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-display font-bold text-billieGold animate-glow">
          Live Web3 Data
        </h3>
        <button
          onClick={() => setIsLive(!isLive)}
          className={`px-3 py-1 rounded-full text-xs font-semibold transition-all duration-300 ${
            isLive
              ? 'bg-neonGreen/20 text-neonGreen border border-neonGreen animate-neon-pulse'
              : 'bg-neutral-700 text-neutral-400 border border-neutral-600'
          }`}
        >
          {isLive ? '● LIVE' : '○ PAUSED'}
        </button>
      </div>

      {/* Data Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {dataItems.map((item, index) => (
          <HolographicCard
            key={item.label}
            glowColor={item.color as any}
            intensity="medium"
            className="p-4"
          >
            <div className="flex items-center justify-between mb-2">
              <div className={`text-${item.color === 'purple' ? 'neonPurple' : item.color === 'gold' ? 'neonGold' : item.color === 'red' ? 'neonRed' : item.color === 'cyan' ? 'neonCyan' : 'neonGreen'}`}>
                {item.icon}
              </div>
              <span className={`text-sm font-bold ${
                item.trend === '↑' ? 'text-neonGreen' : 'text-neonRed'
              }`}>
                {item.trend}
              </span>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-neutral-400 font-cyber uppercase tracking-wider">
                {item.label}
              </p>
              <p className="text-lg font-display font-bold text-billieHeading animate-glow">
                {item.value}
              </p>
            </div>
          </HolographicCard>
        ))}
      </div>

      {/* Network Status */}
      <HolographicCard glowColor="purple" intensity="low" className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-neutral-400 font-cyber uppercase tracking-wider mb-1">
              Network Status
            </p>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-neonGreen rounded-full animate-neon-pulse" />
                <span className="text-sm text-neonGreen font-semibold">Operational</span>
              </div>
              <div className="text-sm text-neutral-400">
                Hash Rate: <span className="text-billieGold">{web3Data.networkHashRate}</span>
              </div>
              <div className="text-sm text-neutral-400">
                Active Users: <span className="text-neonCyan">{web3Data.activeUsers.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>
      </HolographicCard>

      {/* Data Stream Visualization */}
      <div className="relative h-16 overflow-hidden rounded-lg bg-neutral-900/50 border border-neonPurple/30">
        <div className="absolute inset-0 flex items-center">
          {Array.from({ length: 10 }).map((_, i) => (
            <div
              key={i}
              className="absolute text-xs font-cyber text-neonCyan opacity-60 animate-data-stream whitespace-nowrap"
              style={{
                animationDelay: `${i * 0.5}s`,
                animationDuration: '8s',
              }}
            >
              TX:{Math.random().toString(16).substr(2, 8)} 
              GAS:{Math.floor(Math.random() * 100 + 20)} 
              VALUE:{(Math.random() * 10).toFixed(4)}ETH
            </div>
          ))}
        </div>
        <div className="absolute inset-0 bg-gradient-to-r from-neutral-900/50 via-transparent to-neutral-900/50" />
      </div>
    </div>
  );
};

export default Web3DataPanel;
