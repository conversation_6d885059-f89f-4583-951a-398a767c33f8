
import React from 'react';
import { SOCIAL_LINKS } from '../constants';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import HolographicCard from '../components/ui/HolographicCard';
import NeonButton from '../components/ui/NeonButton';
import DAOGovernance from '../components/web3/DAOGovernance';

const CommunityPage: React.FC = () => {
  const discordLink = SOCIAL_LINKS.find(link => link.name === 'Discord');
  const twitterLink = SOCIAL_LINKS.find(link => link.name === 'Twitter');
  const telegramLink = SOCIAL_LINKS.find(link => link.name === 'Telegram');

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div className="text-center mb-12 md:mb-16">
        <h1 className="font-display text-4xl sm:text-5xl font-bold text-billieHeading mb-4 animate-glow">
          Join the <span className="text-billieGold animate-glow">Billieverse</span> Community
        </h1>
        <p className="text-lg text-billieAccent max-w-2xl mx-auto animate-neon-pulse">
          Connect with fellow Web3 enthusiasts, share insights, participate in events, and be part of Billie's growing family.
        </p>

        {/* Web3 Community Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8 max-w-4xl mx-auto">
          <div className="bg-neutral-800/50 p-4 rounded-lg border border-neonPurple/30 animate-hologram">
            <div className="text-2xl font-display font-bold text-neonPurple animate-glow">25K+</div>
            <div className="text-xs text-neutral-400 font-cyber uppercase">Members</div>
          </div>
          <div className="bg-neutral-800/50 p-4 rounded-lg border border-neonGold/30 animate-hologram">
            <div className="text-2xl font-display font-bold text-neonGold animate-glow">1.2K ETH</div>
            <div className="text-xs text-neutral-400 font-cyber uppercase">Treasury</div>
          </div>
          <div className="bg-neutral-800/50 p-4 rounded-lg border border-neonCyan/30 animate-hologram">
            <div className="text-2xl font-display font-bold text-neonCyan animate-glow">42</div>
            <div className="text-xs text-neutral-400 font-cyber uppercase">Proposals</div>
          </div>
          <div className="bg-neutral-800/50 p-4 rounded-lg border border-neonGreen/30 animate-hologram">
            <div className="text-2xl font-display font-bold text-neonGreen animate-glow">89%</div>
            <div className="text-xs text-neutral-400 font-cyber uppercase">Active</div>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        {discordLink && (
          <HolographicCard glowColor="purple" intensity="medium" className="p-6 md:p-8 text-center flex flex-col items-center">
            {React.isValidElement(discordLink.icon) && React.cloneElement(discordLink.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-16 h-16 text-neonPurple mb-4 animate-neon-pulse" })}
            <h2 className="text-2xl font-bold text-billieGold mb-3 animate-glow">Billie's Den (Discord)</h2>
            <p className="text-neutral-300 mb-6 flex-grow">
              The main hub for all things Billie. Chat, share alpha, participate in events, and get direct updates.
            </p>
            <a href={discordLink.url} target="_blank" rel="noopener noreferrer" className="mt-auto">
              <NeonButton variant="purple" size="lg">Join Discord</NeonButton>
            </a>
          </HolographicCard>
        )}
        
        {twitterLink && (
          <HolographicCard glowColor="cyan" intensity="medium" className="p-6 md:p-8 text-center flex flex-col items-center">
             {React.isValidElement(twitterLink.icon) && React.cloneElement(twitterLink.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-16 h-16 text-neonCyan mb-4 animate-neon-pulse" })}
            <h2 className="text-2xl font-bold text-billieGold mb-3 animate-glow">Follow on X (Twitter)</h2>
            <p className="text-neutral-300 mb-6 flex-grow">
              Get the latest news, memes, and Billie's hot takes on the crypto market. Don't miss out!
            </p>
            <a href={twitterLink.url} target="_blank" rel="noopener noreferrer" className="mt-auto">
              <NeonButton variant="cyan" size="lg">Follow on X</NeonButton>
            </a>
          </HolographicCard>
        )}

        {telegramLink && (
           <HolographicCard glowColor="gold" intensity="medium" className="p-6 md:p-8 text-center flex flex-col items-center">
            {React.isValidElement(telegramLink.icon) && React.cloneElement(telegramLink.icon as React.ReactElement<React.SVGProps<SVGSVGElement>>, { className: "w-16 h-16 text-neonGold mb-4 animate-neon-pulse" })}
            <h2 className="text-2xl font-bold text-billieGold mb-3 animate-glow">Telegram Updates</h2>
            <p className="text-neutral-300 mb-6 flex-grow">
              Subscribe to our Telegram channel for important announcements and community news.
            </p>
            <a href={telegramLink.url} target="_blank" rel="noopener noreferrer" className="mt-auto">
              <NeonButton variant="gold" size="lg">Join Telegram</NeonButton>
            </a>
          </HolographicCard>
        )}
      </div>

      {/* DAO Governance Section */}
      <div className="mb-16">
        <DAOGovernance />
      </div>

      <HolographicCard glowColor="purple" intensity="medium" className="p-8">
        <h2 className="text-3xl font-display font-bold text-billieHeading mb-6 text-center animate-glow">
            Share Your <span className="text-billieGold animate-glow">Creations!</span>
        </h2>
        <div className="text-center">
            <p className="text-neutral-300 mb-4 text-lg">
                Got fan art, cool memes, or ideas for Billie? We'd love to see them!
                Share your work in the dedicated channels on our Discord server.
            </p>
            <p className="text-billieAccent mb-6 animate-neon-pulse">
                The best creations might get featured on the website or Billie's social media!
            </p>
            {discordLink && (
                <a href={discordLink.url} target="_blank" rel="noopener noreferrer">
                    <NeonButton variant='purple' size="lg">Show Your Talent!</NeonButton>
                </a>
            )}
        </div>
      </HolographicCard>

      <div className="mt-16 text-center">
        <h3 className="text-2xl font-bold text-billieHeading mb-3">User-Generated Content Spotlight</h3>
        <p className="text-neutral-300 mb-6 max-w-xl mx-auto">
          Billie loves to see your creativity! Soon, we'll feature amazing fan art and memes right here. Keep an eye on this space.
        </p>
        <div className="bg-neutral-800/50 p-6 rounded-lg animate-subtle-pulse">
            <p className="text-billieGold text-lg">Spotlight Gallery Coming Soon!</p>
        </div>
      </div>
    </div>
  );
};

export default CommunityPage;