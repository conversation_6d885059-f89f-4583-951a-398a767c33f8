
import React, { Suspense, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';

// Mock data for interactive elements on Billie
const billieFeatures = [
  { id: 'sunglasses', name: 'Cool Shades', description: 'Purple-tinted, always stylish. Sees through the FUD.', position: { top: '20%', left: '45%' } },
  { id: 'suit', name: 'Power Suit', description: 'Black, sharp, and ready for business in the digital world.', position: { top: '50%', left: '50%' } },
  { id: 'shoes', name: 'Red Kicks', description: 'Fast shoes for outpacing bear markets.', position: { top: '80%', left: '55%' } },
];

// 3D Model Component
const BillieModel: React.FC = () => {
  const { scene } = useGLTF('/billieMoneyBags.glb');
  const modelRef = useRef<THREE.Group>(null);

  // Add subtle floating animation
  useFrame((state) => {
    if (modelRef.current) {
      modelRef.current.position.y = Math.sin(state.clock.elapsedTime) * 0.1;
    }
  });

  return (
    <group ref={modelRef}>
      <primitive object={scene} scale={[4, 4, 4]} />
    </group>
  );
};

// Loading fallback component
const LoadingFallback: React.FC = () => (
  <div className="w-full h-full bg-gradient-to-br from-neonPurple/20 via-billieGold/20 to-neonRed/20 rounded-full flex items-center justify-center animate-float border-2 border-neonPurple animate-neon-pulse shadow-glow-purple">
    <div className="text-billieGold text-lg font-semibold animate-glow font-display">Loading Billie...</div>
  </div>
);

const Billie3DModel: React.FC = () => {
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);

  return (
    <div className="relative w-full max-w-lg aspect-square mx-auto">
      {/* Neon ring around the model */}
      <div className="absolute inset-0 rounded-full border-4 border-neonPurple animate-neon-pulse shadow-glow-purple opacity-60" />
      <div className="absolute inset-2 rounded-full border-2 border-neonGold animate-glow opacity-40" />

      {/* 3D Canvas */}
      <div className="w-full h-full relative z-10">
        <Canvas
          camera={{ position: [0, 0, 5], fov: 50 }}
          style={{ width: '100%', height: '100%', background: 'transparent' }}
        >
          <Suspense fallback={null}>
            {/* Enhanced Lighting with colored lights */}
            <ambientLight intensity={0.3} />
            <directionalLight position={[10, 10, 5]} intensity={1} color="#7C3AED" />
            <pointLight position={[-10, -10, -5]} intensity={0.8} color="#FBBF24" />
            <pointLight position={[5, -5, 5]} intensity={0.6} color="#EF4444" />

            {/* Environment for reflections */}
            <Environment preset="studio" />

            {/* 3D Model */}
            <BillieModel />

            {/* Controls with enhanced rotation */}
            <OrbitControls
              enablePan={false}
              enableZoom={false}
              maxPolarAngle={Math.PI / 2}
              minPolarAngle={Math.PI / 2}
              autoRotate
              autoRotateSpeed={2}
            />
          </Suspense>
        </Canvas>
      </div>

      {/* Floating particles around the model */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 8 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-neonPurple rounded-full animate-particle-float opacity-60"
            style={{
              top: `${20 + (i * 10)}%`,
              left: `${15 + (i * 8)}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${6 + i}s`,
            }}
          />
        ))}
      </div>

      {/* Interactive Feature Tooltips */}
      {billieFeatures.map(feature => (
        <div
          key={feature.id}
          className="absolute p-1 bg-black/50 rounded-full transform -translate-x-1/2 -translate-y-1/2"
          style={{ top: feature.position.top, left: feature.position.left }}
          onMouseEnter={() => setHoveredFeature(feature.name)}
          onMouseLeave={() => setHoveredFeature(null)}
        >
          <div className="w-3 h-3 bg-billieGold rounded-full animate-neon-pulse cursor-pointer shadow-glow-gold"></div>
          {hoveredFeature === feature.name && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-3 bg-billieBlack/90 backdrop-blur-md text-white text-xs rounded-lg shadow-neon-lg border border-neonGold whitespace-nowrap z-10 animate-hologram">
              <h4 className="font-bold text-billieGold animate-glow font-display">{feature.name}</h4>
              <p className="text-neutral-300">{feature.description}</p>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

// Preload the GLB model
useGLTF.preload('/billieMoneyBags.glb');

export default Billie3DModel;
    