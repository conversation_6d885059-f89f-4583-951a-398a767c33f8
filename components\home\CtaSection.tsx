
import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../ui/Button';
import NeonButton from '../ui/NeonButton';
import HolographicCard from '../ui/HolographicCard';
import { UsersIcon, BookOpenIcon, SparklesIcon } from '../ui/Icons';

const CtaSection: React.FC = () => {
  return (
    <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 12 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-neonGold rounded-full animate-particle-float opacity-50"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.8}s`,
              animationDuration: `${6 + i}s`,
            }}
          />
        ))}
      </div>

      <HolographicCard
        glowColor="purple"
        intensity="high"
        className="p-8 md:p-12 text-center relative overflow-hidden"
      >
        {/* Animated gradient background */}
        <div className="absolute inset-0 bg-gradient-to-r from-neonPurple/20 via-cyberPurple/20 to-neonGold/20 animate-pulse" />

        {/* Scanning lines */}
        <div className="absolute inset-0 overflow-hidden">
          {Array.from({ length: 4 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-full h-px bg-gradient-to-r from-transparent via-neonCyan to-transparent animate-data-stream opacity-30"
              style={{
                top: `${25 + i * 20}%`,
                animationDelay: `${i * 1.5}s`,
                animationDuration: '6s',
              }}
            />
          ))}
        </div>

        <div className="relative z-10">
          <h2 className="font-display text-3xl sm:text-4xl font-bold text-billieHeading mb-6 animate-glow">
            Ready to Dive Deeper into <span className="text-neonPurple animate-neon-pulse">Web3</span>?
          </h2>
          <p className="text-lg text-neutral-300 mb-10 max-w-2xl mx-auto">
            Whether you're a seasoned crypto degen or just Web3-curious, Billie's world has something for you. Explore, learn, and become part of the community.
          </p>

          {/* Enhanced CTA buttons */}
          <div className="flex flex-col sm:flex-row flex-wrap justify-center gap-6">
            <Link to="/nfts">
              <NeonButton
                variant="purple"
                size="lg"
                leftIcon={<SparklesIcon className="w-5 h-5" />}
                glowIntensity="high"
              >
                Explore NFTs
              </NeonButton>
            </Link>
            <Link to="/education">
              <NeonButton
                variant="cyan"
                size="lg"
                leftIcon={<BookOpenIcon className="w-5 h-5" />}
                glowIntensity="high"
              >
                Web3 Academy
              </NeonButton>
            </Link>
            <Link to="/community">
              <NeonButton
                variant="gold"
                size="lg"
                leftIcon={<UsersIcon className="w-5 h-5" />}
                glowIntensity="high"
              >
                Join Community
              </NeonButton>
            </Link>
          </div>

          {/* Additional Web3 stats */}
          <div className="grid grid-cols-3 gap-6 mt-12 max-w-lg mx-auto">
            <div className="text-center">
              <div className="text-2xl font-display font-bold text-neonGreen animate-glow">24/7</div>
              <div className="text-xs text-neutral-400 font-cyber uppercase">Active</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-display font-bold text-neonCyan animate-glow">∞</div>
              <div className="text-xs text-neutral-400 font-cyber uppercase">Possibilities</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-display font-bold text-neonGold animate-glow">100%</div>
              <div className="text-xs text-neutral-400 font-cyber uppercase">Decentralized</div>
            </div>
          </div>
        </div>
      </HolographicCard>
    </section>
  );
};

export default CtaSection;
    