import React, { Suspense, useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { useGLTF, OrbitControls, Environment } from '@react-three/drei';
import * as THREE from 'three';

// 3D Model Component for Header Logo
const BillieHeaderModel: React.FC = () => {
  const { scene } = useGLTF('/billieSitting3D.glb');
  const modelRef = useRef<THREE.Group>(null);

  // Add subtle floating animation
  useFrame((state) => {
    if (modelRef.current) {
      modelRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.02;
      modelRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.05;
    }
  });

  return (
    <group ref={modelRef}>
      <primitive object={scene} scale={[1.5, 1.5, 1.5]} />
    </group>
  );
};

// Loading fallback component for header
const HeaderLoadingFallback: React.FC = () => (
  <div className="w-full h-full bg-gradient-to-br from-neonPurple/20 via-billieGold/20 to-neonCyan/20 rounded-full flex items-center justify-center animate-float border-2 border-neonPurple animate-neon-pulse shadow-glow-purple">
    <div className="text-billieGold text-xs font-semibold animate-glow font-display">B</div>
  </div>
);

const BillieHeaderLogo3D: React.FC = () => {
  return (
    <div className="relative w-10 h-10">
      {/* Neon ring around the model */}
      <div className="absolute inset-0 rounded-full border-2 border-neonGold animate-neon-border opacity-60" />
      
      {/* 3D Canvas */}
      <div className="w-full h-full relative z-10">
        <Canvas
          camera={{ position: [0, 0, 6], fov: 45 }}
          style={{ width: '100%', height: '100%', background: 'transparent' }}
        >
          <Suspense fallback={null}>
            {/* Enhanced Lighting with colored lights */}
            <ambientLight intensity={0.4} />
            <directionalLight position={[3, 3, 3]} intensity={0.8} color="#7C3AED" />
            <pointLight position={[-3, -3, 3]} intensity={0.6} color="#FBBF24" />

            {/* Environment for reflections */}
            <Environment preset="studio" />

            {/* 3D Model */}
            <BillieHeaderModel />

            {/* Controls with gentle rotation */}
            <OrbitControls
              enablePan={false}
              enableZoom={false}
              enableRotate={false}
              autoRotate
              autoRotateSpeed={0.3}
            />
          </Suspense>
        </Canvas>
      </div>
      
      {/* Subtle glow effect */}
      <div className="absolute inset-0 rounded-full bg-billieGold/10 animate-neon-pulse shadow-glow-gold" />
    </div>
  );
};

// Preload the GLB model
useGLTF.preload('/billieSitting3D.glb');

export default BillieHeaderLogo3D;
