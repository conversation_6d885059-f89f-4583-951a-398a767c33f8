
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  leftIcon,
  rightIcon,
  className,
  ...props
}) => {
  const baseStyle = "relative inline-flex items-center justify-center font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-opacity-75 transition-all duration-300 ease-in-out transform hover:scale-105 group overflow-hidden";

  const variantStyles = {
    primary: 'bg-billiePurple hover:bg-purple-700 text-white focus:ring-billiePurple shadow-glow-purple hover:shadow-neon-lg animate-neon-pulse',
    secondary: 'bg-billieGold hover:bg-amber-500 text-billieBlack focus:ring-billieGold shadow-glow-gold hover:shadow-neon-lg animate-glow',
    outline: 'bg-transparent hover:bg-neutral-700/50 text-billieAccent border-2 border-billieAccent hover:text-white focus:ring-billieAccent hover:border-neonAccent hover:shadow-neon animate-neon-border',
  };

  const sizeStyles = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-5 py-2.5 text-sm',
    lg: 'px-7 py-3 text-base',
  };

  return (
    <button
      className={`${baseStyle} ${variantStyles[variant]} ${sizeStyles[size]} ${className || ''}`}
      {...props}
    >
      {/* Animated background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-10 transition-opacity duration-300 transform -skew-x-12" />

      {/* Scanning line effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transition-opacity duration-300 transform -translate-x-full group-hover:translate-x-full group-hover:transition-transform group-hover:duration-700" />

      {/* Content */}
      <div className="relative z-10 flex items-center">
        {leftIcon && <span className="mr-2">{leftIcon}</span>}
        <span className="font-display">{children}</span>
        {rightIcon && <span className="ml-2">{rightIcon}</span>}
      </div>
    </button>
  );
};

export default Button;
    