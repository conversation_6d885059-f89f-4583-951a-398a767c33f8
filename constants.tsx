import React from 'react';
import { NavItem, StoryEvent, EducationTopicContent, NFT, CryptoPrice, SocialLink } from './types';
import { UsersIcon, BookOpenIcon, CpuChipIcon, LinkIcon, SparklesIcon, ScaleIcon, LightBulbIcon, PuzzlePieceIcon, ShareIcon } from './components/ui/Icons';

export const NAV_ITEMS: NavItem[] = [
  { name: 'Home', path: '/' },
  { name: '<PERSON>\'s Story', path: '/story' },
  { name: 'NFTs', path: '/nfts' },
  { name: 'Web3 Academy', path: '/education' },
  { name: 'Community', path: '/community' },
];

export const BILLIE_TAGLINE = "The Crypto Bear Who Drinks FOMO Tears, Not Honey.";
export const BILLIE_QUOTE = "I'm not an ordinary bear. I'm Billy the Beer. I drink FOMO tears, not honey.";

export const BILLIE_STORY_EVENTS: StoryEvent[] = [
  { id: '1', year: 'Early Days', title: 'A Spark for Web3', description: '<PERSON>, unlike other bears, found fascination in the burgeoning world of digital ledgers and decentralized dreams.', icon: <SparklesIcon className="w-6 h-6 text-billieGold" /> },
  { id: '2', year: 'The Great Hibernation', title: 'Bear Market Blues', description: 'The crypto winter hit hard. Billie faced setbacks, saw projects crumble, but his spirit for Web3, though tested, never froze.', icon: <PuzzlePieceIcon className="w-6 h-6 text-billiePurple" /> },
  { id: '3', year: 'The Awakening', title: 'Resilience & Re-Education', description: 'During the quiet times, Billie dived deep into learning: Solidity, smart contracts, tokenomics. He sharpened his claws and his mind.', icon: <BookOpenIcon className="w-6 h-6 text-billieAccent" /> },
  { id: '4', year: 'Bull Market Roar', title: 'Triumph and Tears (of FOMO)', description: 'The bull market returned, and Billie, now wiser and stronger, rode the wave. He launched his own projects, helping others navigate the exciting chaos.', icon: <LightBulbIcon className="w-6 h-6 text-billieRed" /> },
];

export const WEB3_EDUCATION_TOPICS: EducationTopicContent[] = [
  {
    title: 'Blockchain Basics',
    summary: 'Understand the foundational technology behind cryptocurrencies and dApps.',
    details: 'A blockchain is a distributed, immutable ledger. It consists of a chain of blocks, where each block contains a batch of transactions. Learn about decentralization, transparency, and security.',
    icon: <LinkIcon className="w-8 h-8 text-billieGold" />
  },
  {
    title: 'Intro to Smart Contracts',
    summary: 'Discover self-executing contracts with the terms of the agreement directly written into code.',
    details: 'Smart contracts automate agreements, running on the blockchain. Solidity is a popular language for writing them. Explore use cases like DeFi, NFTs, and DAOs.',
    icon: <CpuChipIcon className="w-8 h-8 text-billiePurple" />
  },
  {
    title: 'Navigating dApps',
    summary: 'Learn about decentralized applications and how they differ from traditional apps.',
    details: 'dApps run on a peer-to-peer network rather than a single computer. They offer censorship resistance and user control over data. Examples include decentralized exchanges, games, and social media platforms.',
    icon: <SparklesIcon className="w-8 h-8 text-billieAccent" />
  },
  {
    title: 'Crypto Essentials: Trading, Mining, Staking',
    summary: 'Key ways to interact with and potentially profit from cryptocurrencies.',
    details: 'Trading involves buying and selling on exchanges. Mining validates transactions and secures networks (like Bitcoin). Staking involves holding crypto to support a network and earn rewards (common in Proof-of-Stake systems).',
    icon: <ScaleIcon className="w-8 h-8 text-billieRed" />
  },
];

export const MOCK_NFTS: NFT[] = [
  { id: '1', name: 'Billie OG Edition', imageUrl: '/ogBillie.png', price: '1.5 ETH', creator: 'BillieTeam', rarity: 'Legendary', description: 'The first-ever Billie the Bear NFT, a true collector\'s item.' },
  { id: '2', name: 'Cyber Billie', imageUrl: '/cyberBillie.png', price: '0.8 ETH', creator: 'FutureArt', rarity: 'Epic', description: 'Billie dons a futuristic suit, ready for the metaverse.' },
  { id: '3', name: 'FOMO Tears Billie', imageUrl: '/fomoBillie.png', price: '0.3 ETH', creator: 'MemeFactory', rarity: 'Rare', description: 'Billie enjoying his favorite beverage. Limited edition.' },
  { id: '4', name: 'Billie DeFies Gravity', imageUrl: 'https://picsum.photos/seed/defibillie/300/300', price: '0.5 ETH', creator: 'DeFi Collective', rarity: 'Rare', description: 'Billie exploring the world of decentralized finance.' },
];

export const MOCK_CRYPTO_PRICES: CryptoPrice[] = [
  { id: 'btc', name: 'Bitcoin', symbol: 'BTC', price: '$60,420.69', change24h: '+1.2%', iconUrl: 'https://picsum.photos/seed/btcicon/32/32' },
  { id: 'eth', name: 'Ethereum', symbol: 'ETH', price: '$3,005.12', change24h: '-0.5%', iconUrl: 'https://picsum.photos/seed/ethicon/32/32' },
  { id: 'bnb', name: 'BNB', symbol: 'BNB', price: '$580.77', change24h: '+2.1%', iconUrl: 'https://picsum.photos/seed/bnbicon/32/32' },
  { id: 'sol', name: 'Solana', symbol: 'SOL', price: '$150.23', change24h: '****%', iconUrl: 'https://picsum.photos/seed/solicon/32/32' },
];

export const SOCIAL_LINKS: SocialLink[] = [
  { name: 'Twitter', url: 'https://twitter.com/billiethebear', icon: <ShareIcon className="w-6 h-6 hover:text-billiePurple" /> },
  { name: 'Discord', url: 'https://discord.gg/billiethebear', icon: <UsersIcon className="w-6 h-6 hover:text-billiePurple" /> },
  { name: 'Telegram', url: 'https://t.me/billiethebear', icon: <LightBulbIcon className="w-6 h-6 hover:text-billiePurple" /> },
];