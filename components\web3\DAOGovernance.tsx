import React, { useState } from 'react';
import HolographicCard from '../ui/HolographicCard';
import NeonButton from '../ui/NeonButton';
import { CheckIcon, XMarkIcon, ClockIcon, UsersIcon } from '../ui/Icons';

interface Proposal {
  id: string;
  title: string;
  description: string;
  proposer: string;
  status: 'Active' | 'Passed' | 'Failed' | 'Pending';
  votesFor: number;
  votesAgainst: number;
  totalVotes: number;
  endTime: string;
  category: 'Treasury' | 'Protocol' | 'Community' | 'Technical';
}

const DAOGovernance: React.FC = () => {
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null);
  const [userVote, setUserVote] = useState<'for' | 'against' | null>(null);
  const [hasVoted, setHasVoted] = useState(false);

  const proposals: Proposal[] = [
    {
      id: '1',
      title: 'Increase Billie NFT Staking Rewards',
      description: 'Proposal to increase staking rewards for Billie NFT holders from 5% to 8% APY to incentivize long-term holding.',
      proposer: '0x742d...35Bd',
      status: 'Active',
      votesFor: 1250000,
      votesAgainst: 340000,
      totalVotes: 1590000,
      endTime: '2024-02-15T18:00:00Z',
      category: 'Treasury',
    },
    {
      id: '2',
      title: 'Launch Billie Metaverse Land',
      description: 'Allocate 500 ETH from treasury to purchase and develop virtual land in major metaverse platforms.',
      proposer: '0x1a2b...9c8d',
      status: 'Active',
      votesFor: 890000,
      votesAgainst: 1100000,
      totalVotes: 1990000,
      endTime: '2024-02-20T12:00:00Z',
      category: 'Community',
    },
    {
      id: '3',
      title: 'Implement Layer 2 Scaling',
      description: 'Migrate core protocol functions to Polygon to reduce gas fees and improve user experience.',
      proposer: '0x5f6e...2a1b',
      status: 'Passed',
      votesFor: 2100000,
      votesAgainst: 450000,
      totalVotes: 2550000,
      endTime: '2024-02-10T15:30:00Z',
      category: 'Technical',
    },
  ];

  const handleVote = (vote: 'for' | 'against') => {
    setUserVote(vote);
    setHasVoted(true);
    // In a real app, this would interact with the blockchain
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'cyan';
      case 'Passed': return 'green';
      case 'Failed': return 'red';
      case 'Pending': return 'gold';
      default: return 'purple';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Treasury': return 'gold';
      case 'Protocol': return 'purple';
      case 'Community': return 'cyan';
      case 'Technical': return 'green';
      default: return 'purple';
    }
  };

  const calculatePercentage = (votes: number, total: number) => {
    return total > 0 ? ((votes / total) * 100).toFixed(1) : '0.0';
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-display font-bold text-billieGold animate-glow mb-2">
          Billie DAO Governance
        </h2>
        <p className="text-neutral-400 font-cyber">
          Shape the future of the Billie ecosystem through decentralized governance
        </p>
      </div>

      {/* Proposals Grid */}
      <div className="grid gap-6">
        {proposals.map((proposal) => (
          <HolographicCard
            key={proposal.id}
            glowColor={getStatusColor(proposal.status) as any}
            intensity="medium"
            className="p-6"
          >
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-xl font-display font-bold text-billieHeading">
                      {proposal.title}
                    </h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-cyber uppercase ${
                      proposal.status === 'Active' ? 'bg-neonCyan/20 text-neonCyan' :
                      proposal.status === 'Passed' ? 'bg-neonGreen/20 text-neonGreen' :
                      proposal.status === 'Failed' ? 'bg-neonRed/20 text-neonRed' :
                      'bg-neonGold/20 text-neonGold'
                    }`}>
                      {proposal.status}
                    </span>
                  </div>
                  <p className="text-neutral-300 text-sm mb-3">{proposal.description}</p>
                  <div className="flex items-center gap-4 text-xs text-neutral-400">
                    <span>Proposer: <span className="text-neonCyan font-cyber">{proposal.proposer}</span></span>
                    <span className={`px-2 py-1 rounded ${
                      proposal.category === 'Treasury' ? 'bg-neonGold/20 text-neonGold' :
                      proposal.category === 'Protocol' ? 'bg-neonPurple/20 text-neonPurple' :
                      proposal.category === 'Community' ? 'bg-neonCyan/20 text-neonCyan' :
                      'bg-neonGreen/20 text-neonGreen'
                    }`}>
                      {proposal.category}
                    </span>
                  </div>
                </div>
              </div>

              {/* Voting Progress */}
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-neutral-400">Voting Progress</span>
                  <span className="text-neutral-400 font-cyber">
                    {proposal.totalVotes.toLocaleString()} votes
                  </span>
                </div>
                
                <div className="space-y-2">
                  {/* For votes */}
                  <div className="flex items-center gap-3">
                    <CheckIcon className="w-4 h-4 text-neonGreen" />
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-neonGreen">For</span>
                        <span className="text-neonGreen font-bold">
                          {calculatePercentage(proposal.votesFor, proposal.totalVotes)}%
                        </span>
                      </div>
                      <div className="w-full bg-neutral-700 rounded-full h-2">
                        <div
                          className="bg-neonGreen h-2 rounded-full animate-glow"
                          style={{ width: `${calculatePercentage(proposal.votesFor, proposal.totalVotes)}%` }}
                        />
                      </div>
                    </div>
                    <span className="text-neonGreen font-cyber text-sm">
                      {proposal.votesFor.toLocaleString()}
                    </span>
                  </div>

                  {/* Against votes */}
                  <div className="flex items-center gap-3">
                    <XMarkIcon className="w-4 h-4 text-neonRed" />
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-neonRed">Against</span>
                        <span className="text-neonRed font-bold">
                          {calculatePercentage(proposal.votesAgainst, proposal.totalVotes)}%
                        </span>
                      </div>
                      <div className="w-full bg-neutral-700 rounded-full h-2">
                        <div
                          className="bg-neonRed h-2 rounded-full animate-neon-pulse"
                          style={{ width: `${calculatePercentage(proposal.votesAgainst, proposal.totalVotes)}%` }}
                        />
                      </div>
                    </div>
                    <span className="text-neonRed font-cyber text-sm">
                      {proposal.votesAgainst.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Voting Actions */}
              {proposal.status === 'Active' && (
                <div className="flex items-center justify-between pt-4 border-t border-neutral-700">
                  <div className="flex items-center gap-2 text-sm text-neutral-400">
                    <ClockIcon className="w-4 h-4" />
                    <span>Ends: {new Date(proposal.endTime).toLocaleDateString()}</span>
                  </div>
                  
                  {!hasVoted ? (
                    <div className="flex gap-3">
                      <NeonButton
                        variant="green"
                        size="sm"
                        onClick={() => handleVote('for')}
                        leftIcon={<CheckIcon className="w-4 h-4" />}
                      >
                        Vote For
                      </NeonButton>
                      <NeonButton
                        variant="red"
                        size="sm"
                        onClick={() => handleVote('against')}
                        leftIcon={<XMarkIcon className="w-4 h-4" />}
                      >
                        Vote Against
                      </NeonButton>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-neutral-400">Voted:</span>
                      <span className={`px-3 py-1 rounded-full text-xs font-cyber ${
                        userVote === 'for' ? 'bg-neonGreen/20 text-neonGreen' : 'bg-neonRed/20 text-neonRed'
                      }`}>
                        {userVote === 'for' ? 'FOR' : 'AGAINST'}
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </HolographicCard>
        ))}
      </div>

      {/* DAO Stats */}
      <HolographicCard glowColor="purple" intensity="low" className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-display font-bold text-neonPurple animate-glow">
              2.5M
            </div>
            <div className="text-sm text-neutral-400 font-cyber uppercase">Total Holders</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-display font-bold text-neonGold animate-glow">
              1,250 ETH
            </div>
            <div className="text-sm text-neutral-400 font-cyber uppercase">Treasury</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-display font-bold text-neonCyan animate-glow">
              85%
            </div>
            <div className="text-sm text-neutral-400 font-cyber uppercase">Participation</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-display font-bold text-neonGreen animate-glow">
              42
            </div>
            <div className="text-sm text-neutral-400 font-cyber uppercase">Proposals</div>
          </div>
        </div>
      </HolographicCard>
    </div>
  );
};

export default DAOGovernance;
