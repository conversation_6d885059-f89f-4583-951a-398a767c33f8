
import React from 'react';
import { Link } from 'react-router-dom';
import { MOCK_NFTS } from '../../constants';
import { NFT } from '../../types';
import Card from '../ui/Card';
import Button from '../ui/Button';
import HolographicCard from '../ui/HolographicCard';
import NeonButton from '../ui/NeonButton';
import { SparklesIcon, ChevronRightIcon } from '../ui/Icons';

const FeaturedNftCard: React.FC<{ nft: NFT }> = ({ nft }) => {
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'Legendary': return 'gold';
      case 'Epic': return 'purple';
      case 'Rare': return 'cyan';
      default: return 'green';
    }
  };

  return (
    <HolographicCard
      glowColor={getRarityColor(nft.rarity || 'Rare') as any}
      intensity="high"
      className="flex flex-col overflow-hidden group"
    >
      <div className="relative overflow-hidden">
        <img
          src={nft.imageUrl}
          alt={nft.name}
          className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
        />
        {/* Holographic overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-neonPurple/20 via-transparent to-neonGold/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Rarity badge */}
        {nft.rarity && (
          <span className={`absolute top-3 right-3 text-xs px-3 py-1 rounded-full font-cyber font-bold animate-neon-pulse ${
            nft.rarity === 'Legendary' ? 'bg-neonGold/20 text-neonGold border border-neonGold' :
            nft.rarity === 'Epic' ? 'bg-neonPurple/20 text-neonPurple border border-neonPurple' :
            'bg-neonCyan/20 text-neonCyan border border-neonCyan'
          }`}>
            {nft.rarity}
          </span>
        )}
      </div>

      <div className="p-6 flex flex-col flex-grow">
        <h3 className="text-xl font-bold text-billieGold mb-2 animate-glow font-display">{nft.name}</h3>
        <p className="text-sm text-neutral-300 mb-4 flex-grow">{nft.description}</p>

        {nft.price && (
          <div className="flex items-center justify-between mb-4">
            <span className="text-xs text-neutral-400 font-cyber uppercase">Price</span>
            <span className="text-lg font-bold text-neonGreen animate-glow font-display">{nft.price}</span>
          </div>
        )}

        {nft.creator && (
          <div className="flex items-center justify-between mb-4 text-xs">
            <span className="text-neutral-400 font-cyber uppercase">Creator</span>
            <span className="text-neonCyan">{nft.creator}</span>
          </div>
        )}

        <NeonButton
          variant={getRarityColor(nft.rarity || 'Rare') as any}
          size="sm"
          className="mt-auto w-full"
          leftIcon={<SparklesIcon className="w-4 h-4" />}
        >
          View Details
        </NeonButton>
      </div>
    </HolographicCard>
  );
};

const FeaturedNfts: React.FC = () => {
  const featured = MOCK_NFTS.slice(0, 3); // Show first 3 NFTs

  return (
    <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 6 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-neonPurple rounded-full animate-particle-float opacity-30"
            style={{
              top: `${20 + (i * 15)}%`,
              left: `${10 + (i * 15)}%`,
              animationDelay: `${i * 1.5}s`,
              animationDuration: `${8 + i}s`,
            }}
          />
        ))}
      </div>

      <div className="text-center mb-12 relative z-10">
        <h2 className="font-display text-3xl sm:text-4xl font-bold text-billieHeading mb-4 animate-glow">
          Featured <span className="text-billiePurple animate-neon-pulse">Billie NFTs</span>
        </h2>
        <p className="text-lg text-neutral-300 max-w-2xl mx-auto">
          Get your paws on exclusive Billie the Bear digital collectibles. Each NFT is a unique piece of the Billieverse.
        </p>

        {/* NFT Collection Stats */}
        <div className="flex justify-center gap-8 mt-6">
          <div className="text-center">
            <div className="text-2xl font-display font-bold text-neonGold animate-glow">1,000</div>
            <div className="text-xs text-neutral-400 font-cyber uppercase">Total Supply</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-display font-bold text-neonPurple animate-glow">750</div>
            <div className="text-xs text-neutral-400 font-cyber uppercase">Minted</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-display font-bold text-neonCyan animate-glow">2.5 ETH</div>
            <div className="text-xs text-neutral-400 font-cyber uppercase">Floor Price</div>
          </div>
        </div>
      </div>

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 relative z-10">
        {featured.map(nft => (
          <FeaturedNftCard key={nft.id} nft={nft} />
        ))}
      </div>

      <div className="text-center mt-12 relative z-10">
        <Link to="/nfts">
          <NeonButton
            variant="purple"
            size="lg"
            rightIcon={<ChevronRightIcon className="w-5 h-5" />}
            glowIntensity="high"
          >
            Explore Full Collection
          </NeonButton>
        </Link>
      </div>
    </section>
  );
};

export default FeaturedNfts;
    