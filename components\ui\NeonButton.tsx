import React from 'react';

interface NeonButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'purple' | 'gold' | 'red' | 'cyan' | 'green';
  size?: 'sm' | 'md' | 'lg';
  glowIntensity?: 'low' | 'medium' | 'high';
  children: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const NeonButton: React.FC<NeonButtonProps> = ({
  variant = 'purple',
  size = 'md',
  glowIntensity = 'medium',
  children,
  leftIcon,
  rightIcon,
  className = '',
  ...props
}) => {
  const baseClasses = "relative inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-opacity-50 overflow-hidden group";

  const variantClasses = {
    purple: {
      bg: 'bg-billiePurple/20 hover:bg-billiePurple/30',
      text: 'text-neonPurple',
      border: 'border-2 border-neonPurple',
      glow: 'shadow-glow-purple hover:shadow-neon-lg',
      focus: 'focus:ring-neonPurple',
    },
    gold: {
      bg: 'bg-billieGold/20 hover:bg-billieGold/30',
      text: 'text-neonGold',
      border: 'border-2 border-neonGold',
      glow: 'shadow-glow-gold hover:shadow-neon-lg',
      focus: 'focus:ring-neonGold',
    },
    red: {
      bg: 'bg-billieRed/20 hover:bg-billieRed/30',
      text: 'text-neonRed',
      border: 'border-2 border-neonRed',
      glow: 'shadow-glow-red hover:shadow-neon-lg',
      focus: 'focus:ring-neonRed',
    },
    cyan: {
      bg: 'bg-neonCyan/20 hover:bg-neonCyan/30',
      text: 'text-neonCyan',
      border: 'border-2 border-neonCyan',
      glow: 'shadow-neon hover:shadow-neon-lg',
      focus: 'focus:ring-neonCyan',
    },
    green: {
      bg: 'bg-neonGreen/20 hover:bg-neonGreen/30',
      text: 'text-neonGreen',
      border: 'border-2 border-neonGreen',
      glow: 'shadow-neon hover:shadow-neon-lg',
      focus: 'focus:ring-neonGreen',
    },
  };

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg',
  };

  const glowClasses = {
    low: 'animate-neon-pulse',
    medium: 'animate-glow',
    high: 'animate-neon-pulse animate-glow',
  };

  const currentVariant = variantClasses[variant];

  return (
    <button
      className={`
        ${baseClasses}
        ${currentVariant.bg}
        ${currentVariant.text}
        ${currentVariant.border}
        ${currentVariant.glow}
        ${currentVariant.focus}
        ${sizeClasses[size]}
        ${glowClasses[glowIntensity]}
        ${className}
      `}
      {...props}
    >
      {/* Animated background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-current to-transparent opacity-0 group-hover:opacity-10 transition-opacity duration-300 transform -skew-x-12 group-hover:animate-pulse" />
      
      {/* Scanning line effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transition-opacity duration-300 transform -translate-x-full group-hover:translate-x-full group-hover:transition-transform group-hover:duration-700" />
      
      {/* Content */}
      <div className="relative flex items-center space-x-2 z-10">
        {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
        <span className="font-display font-bold tracking-wide">{children}</span>
        {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
      </div>
      
      {/* Corner accents */}
      <div className="absolute top-0 left-0 w-2 h-2 border-t-2 border-l-2 border-current opacity-60" />
      <div className="absolute top-0 right-0 w-2 h-2 border-t-2 border-r-2 border-current opacity-60" />
      <div className="absolute bottom-0 left-0 w-2 h-2 border-b-2 border-l-2 border-current opacity-60" />
      <div className="absolute bottom-0 right-0 w-2 h-2 border-b-2 border-r-2 border-current opacity-60" />
    </button>
  );
};

export default NeonButton;
