
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" /> <!-- Replace with actual favicon -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Billie the Bear - Web3 Adventure</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              billiePurple: '#7C3AED', // purple-600
              billieGold: '#FBBF24',   // amber-400
              billieBlack: '#171717',  // neutral-900 (suit, dark theme background)
              billieRed: '#EF4444',    // red-600
              billieBodyText: '#E2E8F0', // slate-200
              billieHeading: '#FFFFFF',
              billieAccent: '#A78BFA', // violet-400
              // Neon variants for web3 flair
              neonPurple: '#8B5CF6',
              neonGold: '#FCD34D',
              neonRed: '#F87171',
              neonAccent: '#C4B5FD',
              neonCyan: '#06B6D4',
              neonGreen: '#10B981',
              // Cyberpunk gradients
              cyberPurple: '#6366F1',
              cyberPink: '#EC4899',
              cyberBlue: '#3B82F6',
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
              display: ['Orbitron', 'sans-serif'],
              cyber: ['Courier New', 'monospace'],
            },
            animation: {
              'subtle-pulse': 'subtlePulse 2s infinite ease-in-out',
              'float': 'float 6s ease-in-out infinite',
              'neon-pulse': 'neonPulse 2s infinite ease-in-out',
              'glow': 'glow 2s infinite ease-in-out alternate',
              'matrix-rain': 'matrixRain 20s linear infinite',
              'cyber-glitch': 'cyberGlitch 0.3s infinite',
              'particle-float': 'particleFloat 8s infinite ease-in-out',
              'hologram': 'hologram 3s infinite ease-in-out',
              'neon-border': 'neonBorder 2s infinite ease-in-out',
              'data-stream': 'dataStream 15s linear infinite',
              'spin-slow': 'spin 8s linear infinite',
              'bounce-slow': 'bounce 3s infinite',
            },
            keyframes: {
              subtlePulse: {
                '0%, 100%': { opacity: '1' },
                '50%': { opacity: '.7' },
              },
              float: {
                '0%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-10px)' },
                '100%': { transform: 'translateY(0px)' },
              },
              neonPulse: {
                '0%, 100%': {
                  boxShadow: '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
                  opacity: '1'
                },
                '50%': {
                  boxShadow: '0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor',
                  opacity: '0.8'
                },
              },
              glow: {
                '0%': {
                  textShadow: '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
                  filter: 'brightness(1)'
                },
                '100%': {
                  textShadow: '0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor',
                  filter: 'brightness(1.2)'
                },
              },
              matrixRain: {
                '0%': { transform: 'translateY(-100vh)' },
                '100%': { transform: 'translateY(100vh)' },
              },
              cyberGlitch: {
                '0%, 100%': { transform: 'translate(0)' },
                '20%': { transform: 'translate(-2px, 2px)' },
                '40%': { transform: 'translate(-2px, -2px)' },
                '60%': { transform: 'translate(2px, 2px)' },
                '80%': { transform: 'translate(2px, -2px)' },
              },
              particleFloat: {
                '0%, 100%': { transform: 'translateY(0px) rotate(0deg)' },
                '33%': { transform: 'translateY(-20px) rotate(120deg)' },
                '66%': { transform: 'translateY(10px) rotate(240deg)' },
              },
              hologram: {
                '0%, 100%': { opacity: '0.8', transform: 'scale(1)' },
                '50%': { opacity: '1', transform: 'scale(1.02)' },
              },
              neonBorder: {
                '0%, 100%': { borderColor: 'currentColor', boxShadow: '0 0 5px currentColor' },
                '50%': { borderColor: 'transparent', boxShadow: '0 0 20px currentColor' },
              },
              dataStream: {
                '0%': { transform: 'translateX(-100%)' },
                '100%': { transform: 'translateX(100vw)' },
              },
            },
            boxShadow: {
              'neon': '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
              'neon-lg': '0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor',
              'cyber': '0 4px 14px 0 rgba(139, 92, 246, 0.39)',
              'glow-purple': '0 0 20px rgba(139, 92, 246, 0.5)',
              'glow-gold': '0 0 20px rgba(252, 211, 77, 0.5)',
              'glow-red': '0 0 20px rgba(248, 113, 113, 0.5)',
            },
            backgroundImage: {
              'cyber-grid': 'linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px)',
              'neon-gradient': 'linear-gradient(45deg, #7C3AED, #EC4899, #06B6D4, #FBBF24)',
              'matrix-bg': 'radial-gradient(ellipse at center, rgba(139, 92, 246, 0.1) 0%, transparent 70%)',
            },
            backgroundSize: {
              'grid': '50px 50px',
            },
          }
        }
      }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&family=Orbitron:wght@700;900&display=swap" rel="stylesheet">
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>

</head>
  <body class="bg-billieBlack text-billieBodyText font-sans">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
    